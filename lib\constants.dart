import 'package:flutter/material.dart';

// TODO: Replace with your actual Google Maps API Key
// Get your API key from: https://console.cloud.google.com/
// Enable Maps SDK for Android, Maps SDK for iOS, and Directions API
const String googleApiKey = 'YOUR_GOOGLE_MAPS_API_KEY_HERE';

// App Theme Colors
const Color primaryColor = Color(0xFF2196F3); // Blue color similar to Uber
const Color secondaryColor = Color(0xFF1976D2);
const Color accentColor = Color(0xFF03DAC6);

// App Constants
const double defaultPadding = 16.0;
const double defaultRadius = 8.0;

// Vehicle Types
enum VehicleType { bike, car, truck }

// App Strings
class AppStrings {
  static const String appName = 'Live Location Tracking';
  static const String bookRide = 'Book Your Ride';
  static const String chooseVehicle = 'Choose your vehicle';
  static const String startTracking = 'Start Live Tracking';
  static const String lookingForDriver = 'Looking for driver...';
  static const String driverAssigned = 'Driver assigned - On the way';
  static const String driverArrived = 'Driver has arrived!';
}
