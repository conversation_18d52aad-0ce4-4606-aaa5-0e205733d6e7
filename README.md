# Live Location Tracking App

A simple Flutter app for live location tracking similar to Uber, with Google Maps integration. This app allows users to select vehicle types (bike, car, truck) and track their location in real-time.

## Features

- **Vehicle Selection**: Choose between bike, car, or truck
- **Live Location Tracking**: Real-time location updates with Google Maps
- **Route Display**: Shows route between pickup and destination
- **Driver Simulation**: Simulates driver assignment and movement
- **Clean UI**: Modern, Uber-like interface design

## Screenshots

The app consists of two main pages:
1. **Vehicle Selection Page**: Choose your vehicle and set pickup/destination
2. **Live Tracking Page**: Real-time map with driver location and route

## Setup Instructions

### 1. Google Maps API Key Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the following APIs:
   - Maps SDK for Android
   - Maps SDK for iOS
   - Directions API
4. Create credentials (API Key)
5. Replace the API key in the following files:

**For Flutter (lib/constants.dart):**
```dart
const String googleApiKey = 'YOUR_ACTUAL_API_KEY_HERE';
```

**For Android (android/app/src/main/AndroidManifest.xml):**
```xml
<meta-data
    android:name="com.google.android.geo.API_KEY"
    android:value="YOUR_ACTUAL_API_KEY_HERE" />
```

### 2. Install Dependencies

```bash
flutter pub get
```

### 3. Run the App

```bash
flutter run
```

## Dependencies

- `google_maps_flutter: ^2.5.0` - Google Maps integration
- `location: ^8.0.1` - Location services
- `flutter_polyline_points: ^3.0.1` - Route polylines
- `flutter_svg: ^2.2.0` - SVG support

## Permissions

The app requires location permissions:

**Android**: Automatically configured in AndroidManifest.xml
- ACCESS_FINE_LOCATION
- ACCESS_COARSE_LOCATION
- INTERNET

**iOS**: Automatically configured in Info.plist
- NSLocationWhenInUseUsageDescription
- NSLocationAlwaysAndWhenInUseUsageDescription

## How to Use

1. **Launch the app** - Opens vehicle selection page
2. **Choose vehicle type** - Select bike, car, or truck
3. **Set destination** - Enter your destination address
4. **Start tracking** - Tap "Start Live Tracking"
5. **View live updates** - See real-time driver location and route

## Customization

You can customize:
- Vehicle types and pricing in `vehicle_selection_page.dart`
- Map styling and markers in `live_tracking_page.dart`
- App colors and theme in `constants.dart`
- Default locations and coordinates

## Note

This is a demo app with simulated driver movement. In a production app, you would:
- Integrate with a real backend service
- Use actual driver locations from GPS
- Implement real-time database updates
- Add payment integration
- Include user authentication

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
