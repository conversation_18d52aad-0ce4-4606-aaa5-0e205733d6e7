import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:location/location.dart';
import 'package:flutter_polyline_points/flutter_polyline_points.dart';
import '../constants.dart';

class LiveTrackingPage extends StatefulWidget {
  final String vehicleType;
  final String pickup;
  final String destination;

  const LiveTrackingPage({
    super.key,
    required this.vehicleType,
    required this.pickup,
    required this.destination,
  });

  @override
  State<LiveTrackingPage> createState() => _LiveTrackingPageState();
}

class _LiveTrackingPageState extends State<LiveTrackingPage> {
  final Completer<GoogleMapController> _controller = Completer();
  Location location = Location();
  LocationData? currentLocation;
  LocationData? driverLocation;

  // Sample destination coordinates (you can modify these)
  static const LatLng destinationCoords = LatLng(37.4276, -122.0859);

  List<LatLng> polylineCoordinates = [];
  Set<Marker> markers = {};
  Set<Polyline> polylines = {};

  Timer? locationTimer;
  bool isDriverAssigned = false;
  String trackingStatus = "Looking for driver...";
  double estimatedTime = 0;
  double distance = 0;

  @override
  void initState() {
    super.initState();
    _initializeTracking();
  }

  @override
  void dispose() {
    locationTimer?.cancel();
    super.dispose();
  }

  void _initializeTracking() async {
    await _getCurrentLocation();
    await _simulateDriverAssignment();
    _startLocationUpdates();
  }

  Future<void> _getCurrentLocation() async {
    bool serviceEnabled;
    PermissionStatus permissionGranted;

    serviceEnabled = await location.serviceEnabled();
    if (!serviceEnabled) {
      serviceEnabled = await location.requestService();
      if (!serviceEnabled) return;
    }

    permissionGranted = await location.hasPermission();
    if (permissionGranted == PermissionStatus.denied) {
      permissionGranted = await location.requestPermission();
      if (permissionGranted != PermissionStatus.granted) return;
    }

    currentLocation = await location.getLocation();
    if (currentLocation != null) {
      _updateMarkers();
      _getPolyline();
    }
  }

  Future<void> _simulateDriverAssignment() async {
    // Simulate driver assignment after 3 seconds
    await Future.delayed(const Duration(seconds: 3));

    if (mounted) {
      setState(() {
        isDriverAssigned = true;
        trackingStatus = "Driver assigned - On the way";
        // Simulate driver location near current location
        driverLocation = LocationData.fromMap({
          'latitude': (currentLocation?.latitude ?? 37.4219) + 0.001,
          'longitude': (currentLocation?.longitude ?? -122.0841) - 0.001,
        });
      });
      _updateMarkers();
      _calculateDistance();
    }
  }

  void _startLocationUpdates() {
    locationTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _updateDriverLocation();
    });
  }

  void _updateDriverLocation() {
    if (driverLocation != null && currentLocation != null) {
      // Simulate driver moving towards pickup location
      double latDiff =
          (currentLocation!.latitude! - driverLocation!.latitude!) * 0.1;
      double lngDiff =
          (currentLocation!.longitude! - driverLocation!.longitude!) * 0.1;

      setState(() {
        driverLocation = LocationData.fromMap({
          'latitude': driverLocation!.latitude! + latDiff,
          'longitude': driverLocation!.longitude! + lngDiff,
        });
      });

      _updateMarkers();
      _calculateDistance();
    }
  }

  void _updateMarkers() {
    markers.clear();

    if (currentLocation != null) {
      markers.add(
        Marker(
          markerId: const MarkerId('pickup'),
          position:
              LatLng(currentLocation!.latitude!, currentLocation!.longitude!),
          infoWindow: InfoWindow(title: 'Pickup: ${widget.pickup}'),
          icon:
              BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
        ),
      );
    }

    markers.add(
      const Marker(
        markerId: MarkerId('destination'),
        position: destinationCoords,
        infoWindow: InfoWindow(title: 'Destination'),
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
      ),
    );

    if (driverLocation != null) {
      markers.add(
        Marker(
          markerId: const MarkerId('driver'),
          position:
              LatLng(driverLocation!.latitude!, driverLocation!.longitude!),
          infoWindow:
              InfoWindow(title: '${widget.vehicleType.toUpperCase()} Driver'),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
        ),
      );
    }

    if (mounted) setState(() {});
  }

  void _getPolyline() async {
    if (currentLocation == null) return;

    PolylinePoints polylinePoints = PolylinePoints();
    PolylineResult result = await polylinePoints.getRouteBetweenCoordinates(
      googleApiKey,
      PointLatLng(currentLocation!.latitude!, currentLocation!.longitude!),
      PointLatLng(destinationCoords.latitude, destinationCoords.longitude),
    );

    if (result.points.isNotEmpty) {
      polylineCoordinates.clear();
      for (var point in result.points) {
        polylineCoordinates.add(LatLng(point.latitude, point.longitude));
      }

      polylines.add(
        Polyline(
          polylineId: const PolylineId('route'),
          points: polylineCoordinates,
          color: primaryColor,
          width: 4,
        ),
      );

      if (mounted) setState(() {});
    }
  }

  void _calculateDistance() {
    if (currentLocation != null && driverLocation != null) {
      distance = _calculateDistanceBetween(
        currentLocation!.latitude!,
        currentLocation!.longitude!,
        driverLocation!.latitude!,
        driverLocation!.longitude!,
      );

      // Estimate time based on distance (rough calculation)
      estimatedTime = distance * 2; // 2 minutes per km

      if (distance < 0.1) {
        setState(() {
          trackingStatus = "Driver has arrived!";
        });
      } else {
        setState(() {
          trackingStatus = "Driver is ${distance.toStringAsFixed(1)} km away";
        });
      }
    }
  }

  double _calculateDistanceBetween(
      double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371; // Earth's radius in kilometers

    double dLat = _degreesToRadians(lat2 - lat1);
    double dLon = _degreesToRadians(lon2 - lon1);

    double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(_degreesToRadians(lat1)) *
            cos(_degreesToRadians(lat2)) *
            sin(dLon / 2) *
            sin(dLon / 2);

    double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    return earthRadius * c;
  }

  double _degreesToRadians(double degrees) {
    return degrees * (pi / 180);
  }

  IconData _getVehicleIcon() {
    switch (widget.vehicleType) {
      case 'bike':
        return Icons.motorcycle;
      case 'truck':
        return Icons.local_shipping;
      default:
        return Icons.directions_car;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Tracking ${widget.vehicleType.toUpperCase()}'),
        centerTitle: true,
      ),
      body: currentLocation == null
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Status Card
                Container(
                  width: double.infinity,
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.2),
                        spreadRadius: 2,
                        blurRadius: 5,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(_getVehicleIcon(),
                              color: primaryColor, size: 30),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  trackingStatus,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                if (estimatedTime > 0)
                                  Text(
                                    'ETA: ${estimatedTime.toStringAsFixed(0)} min',
                                    style: TextStyle(
                                      color: Colors.grey[600],
                                      fontSize: 14,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                          if (isDriverAssigned)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.green,
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: const Text(
                                'LIVE',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Google Map
                Expanded(
                  child: GoogleMap(
                    initialCameraPosition: CameraPosition(
                      target: LatLng(
                        currentLocation!.latitude!,
                        currentLocation!.longitude!,
                      ),
                      zoom: 14,
                    ),
                    markers: markers,
                    polylines: polylines,
                    onMapCreated: (GoogleMapController controller) {
                      _controller.complete(controller);
                    },
                    myLocationEnabled: true,
                    myLocationButtonEnabled: true,
                  ),
                ),
              ],
            ),
    );
  }
}
