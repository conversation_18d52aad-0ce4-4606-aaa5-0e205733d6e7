import 'package:flutter/material.dart';
import 'package:googlemaptracking/pages/live_tracking_page.dart';
import '../constants.dart';

class VehicleSelectionPage extends StatefulWidget {
  const VehicleSelectionPage({super.key});

  @override
  State<VehicleSelectionPage> createState() => _VehicleSelectionPageState();
}

class _VehicleSelectionPageState extends State<VehicleSelectionPage> {
  String selectedVehicle = 'car';
  final TextEditingController _pickupController = TextEditingController();
  final TextEditingController _destinationController = TextEditingController();

  final List<Map<String, dynamic>> vehicles = [
    {
      'type': 'bike',
      'name': 'Bike',
      'icon': Icons.motorcycle,
      'price': '\$5-8',
      'time': '10-15 min'
    },
    {
      'type': 'car',
      'name': 'Car',
      'icon': Icons.directions_car,
      'price': '\$12-18',
      'time': '5-10 min'
    },
    {
      'type': 'truck',
      'name': 'Truck',
      'icon': Icons.local_shipping,
      'price': '\$25-35',
      'time': '15-20 min'
    },
  ];

  @override
  void initState() {
    super.initState();
    // Set default locations
    _pickupController.text = "Current Location";
    _destinationController.text = "Enter destination";
  }

  @override
  void dispose() {
    _pickupController.dispose();
    _destinationController.dispose();
    super.dispose();
  }

  void _startTracking() {
    if (_destinationController.text.isEmpty || 
        _destinationController.text == "Enter destination") {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a destination'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => LiveTrackingPage(
          vehicleType: selectedVehicle,
          pickup: _pickupController.text,
          destination: _destinationController.text,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Book Your Ride'),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Location Input Section
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(defaultPadding),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: const BoxDecoration(
                            color: Colors.green,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: TextField(
                            controller: _pickupController,
                            decoration: const InputDecoration(
                              hintText: 'Pickup location',
                              border: InputBorder.none,
                            ),
                            readOnly: true,
                          ),
                        ),
                        const Icon(Icons.my_location, color: Colors.green),
                      ],
                    ),
                    const Divider(),
                    Row(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: TextField(
                            controller: _destinationController,
                            decoration: const InputDecoration(
                              hintText: 'Where to?',
                              border: InputBorder.none,
                            ),
                            onTap: () {
                              if (_destinationController.text == "Enter destination") {
                                _destinationController.clear();
                              }
                            },
                          ),
                        ),
                        const Icon(Icons.location_on, color: Colors.red),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Vehicle Selection Section
            const Text(
              'Choose your vehicle',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Expanded(
              child: ListView.builder(
                itemCount: vehicles.length,
                itemBuilder: (context, index) {
                  final vehicle = vehicles[index];
                  final isSelected = selectedVehicle == vehicle['type'];
                  
                  return Card(
                    margin: const EdgeInsets.only(bottom: 12),
                    elevation: isSelected ? 8 : 2,
                    color: isSelected ? primaryColor.withOpacity(0.1) : null,
                    child: ListTile(
                      contentPadding: const EdgeInsets.all(16),
                      leading: Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: isSelected ? primaryColor : Colors.grey[200],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          vehicle['icon'],
                          color: isSelected ? Colors.white : Colors.grey[600],
                          size: 28,
                        ),
                      ),
                      title: Text(
                        vehicle['name'],
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: isSelected ? primaryColor : null,
                        ),
                      ),
                      subtitle: Text(
                        '${vehicle['time']} • ${vehicle['price']}',
                        style: TextStyle(
                          color: isSelected ? primaryColor : Colors.grey[600],
                        ),
                      ),
                      trailing: isSelected
                          ? const Icon(Icons.check_circle, color: primaryColor)
                          : null,
                      onTap: () {
                        setState(() {
                          selectedVehicle = vehicle['type'];
                        });
                      },
                    ),
                  );
                },
              ),
            ),
            
            // Book Ride Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _startTracking,
                child: const Text(
                  'Start Live Tracking',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
